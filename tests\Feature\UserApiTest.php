<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class UserApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo roles cần thiết
        Role::create(['name' => 'admin', 'vi_name' => 'Quản trị viên']);
        Role::create(['name' => 'user', 'vi_name' => 'Người dùng']);
    }

    public function test_can_get_users_list_when_authenticated()
    {
        // Tạo user để test
        $user = User::factory()->create();
        $user->assignRole('admin');

        // Tạo thêm một số users khác
        $users = User::factory()->count(5)->create();
        foreach ($users as $u) {
            $u->assignRole('user');
        }

        // Gọi API với authentication
        $response = $this->actingAs($user)
            ->getJson('/api/users');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'email',
                        'created_at',
                        'updated_at',
                        'role'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'total',
                    'last_page'
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertCount(6, $response->json('data')); // 1 admin + 5 users
    }

    public function test_cannot_get_users_list_when_not_authenticated()
    {
        $response = $this->getJson('/api/users');

        $response->assertStatus(401);
    }

    public function test_can_search_users()
    {
        $user = User::factory()->create(['name' => 'Admin User']);
        $user->assignRole('admin');

        $searchUser = User::factory()->create(['name' => 'John Doe']);
        $searchUser->assignRole('user');

        $otherUser = User::factory()->create(['name' => 'Jane Smith']);
        $otherUser->assignRole('user');

        $response = $this->actingAs($user)
            ->getJson('/api/users?search=John');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('John Doe', $data[0]['name']);
    }

    public function test_can_filter_users_by_role()
    {
        $adminUser = User::factory()->create();
        $adminUser->assignRole('admin');

        $regularUser = User::factory()->create();
        $regularUser->assignRole('user');

        $response = $this->actingAs($adminUser)
            ->getJson('/api/users?role=admin');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('admin', $data[0]['role']['name']);
    }

    public function test_pagination_works()
    {
        $user = User::factory()->create();
        $user->assignRole('admin');

        // Tạo 25 users
        $users = User::factory()->count(25)->create();
        foreach ($users as $u) {
            $u->assignRole('user');
        }

        $response = $this->actingAs($user)
            ->getJson('/api/users?per_page=10');

        $response->assertStatus(200);
        
        $meta = $response->json('meta');
        $this->assertEquals(10, $meta['per_page']);
        $this->assertEquals(26, $meta['total']); // 25 + 1 admin
        $this->assertEquals(3, $meta['last_page']); // ceil(26/10)
        $this->assertCount(10, $response->json('data'));
    }
}
