<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'username' => $this->email, // Sử dụng email làm username
            'email_verified_at' => $this->email_verified_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'status' => $this->status ?? 'Đang hoạt động', // Trạng thái mặc định
            'unit' => $this->unit ?? '', // Đơn vị làm việc
            
            // Role information
            'role' => $this->whenLoaded('roles', function () {
                $role = $this->roles->first();
                return $role ? [
                    'id' => $role->id,
                    'name' => $role->name,
                    'vi_name' => $role->vi_name ?? $role->name,
                ] : null;
            }),
            
            // Permissions
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'vi_name' => $permission->vi_name ?? $permission->name,
                    ];
                });
            }),

            'id_xa' => $this->id_xa ?? null, // ID xã nếu có
        ];
    }
}
