<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

const users = ref<any[]>([])
const loading = ref(false)
const error = ref('')
const searchTerm = ref('')
const selectedRole = ref('')
const selectedStatus = ref('')
const pagination = ref({
  current_page: 1,
  per_page: 15,
  total: 0,
  last_page: 1,
  from: 0,
  to: 0
})

const fetchUsers = async (page = 1) => {
  loading.value = true
  error.value = ''
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: pagination.value.per_page.toString()
    })

    if (searchTerm.value) {
      params.append('search', searchTerm.value)
    }
    if (selectedRole.value) {
      params.append('role', selectedRole.value)
    }
    if (selectedStatus.value) {
      params.append('status', selectedStatus.value)
    }

    const res = await axios.get(`/api/users?${params}`)

    if (res.data.success) {
      users.value = res.data.data || []
      if (res.data.meta) {
        pagination.value = res.data.meta
      }
    } else {
      users.value = res.data.data || res.data || []
    }
  } catch (e: any) {
    error.value = 'Không thể tải danh sách người dùng.'
    console.error('Error fetching users:', e)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUsers()
})

// Watch for search and filter changes with debounce
let debounceTimer: number
watch([searchTerm, selectedRole, selectedStatus], () => {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    fetchUsers(1) // Reset to first page when filters change
  }, 500)
})

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Đang hoạt động':
      return 'bg-green-100 text-green-800'
    case 'Ngừng hoạt động':
      return 'bg-yellow-100 text-yellow-800'
    case 'Bị khóa':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Quản Lý Người Dùng
      </h2>
      <Button>
        <Icon name="Plus" class="h-4 w-4 mr-2" />
        Thêm người dùng
      </Button>
    </div>
    <div class="flex gap-4 mb-4">
      <Input
        v-model="searchTerm"
        placeholder="Tìm kiếm người dùng..."
        class="max-w-xs"
      />
      <select v-model="selectedRole" class="border rounded px-2 py-1">
        <option value="">Tất cả vai trò</option>
        <option value="admin">Quản trị viên</option>
        <option value="specialist_data">Chuyên viên Dữ liệu</option>
        <option value="specialist_report">Chuyên viên Báo cáo</option>
        <option value="specialist_user">Chuyên viên Người dùng</option>
        <option value="guest">Khách</option>
      </select>
      <select v-model="selectedStatus" class="border rounded px-2 py-1">
        <option value="">Tất cả trạng thái</option>
        <option value="Đang hoạt động">Đang hoạt động</option>
        <option value="Ngừng hoạt động">Ngừng hoạt động</option>
        <option value="Bị khóa">Bị khóa</option>
      </select>
    </div>
    <div v-if="loading" class="p-4 text-center text-gray-500">Đang tải...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
    <div v-else class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Tên người dùng
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Email
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Vai trò
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Đơn vị
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Trạng thái
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Ngày tạo
            </th>
            <th class="p-3 w-[50px]"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(user, index) in users" :key="user.id || index" class="border-b">
            <td class="p-3">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full bg-muted flex items-center justify-center font-semibold">
                  {{ user.name?.charAt(0) }}
                </div>
                <div>
                  <div class="font-medium">
                    {{ user.name }}
                  </div>
                  <div class="text-sm text-muted-foreground">
                    {{ user.username }}
                  </div>
                </div>
              </div>
            </td>
            <td class="p-3 text-sm">
              {{ user.email }}
            </td>
            <td class="p-3 text-sm">
              {{ user.role?.vi_name || user.role?.name || user.role || '' }}
            </td>
            <td class="p-3 text-sm">
              {{ user.unit || '' }}
            </td>
            <td class="p-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(user.status)"
              >
                {{ user.status || 'Đang hoạt động' }}
              </span>
            </td>
            <td class="p-3 text-sm">
              {{ user.created_at ? (new Date(user.created_at).toLocaleDateString('vi-VN')) : '' }}
            </td>
            <td class="p-3">
              <Button variant="ghost" size="icon">
                <Icon name="MoreHorizontal" class="h-4 w-4" />
              </Button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-between items-center mt-4 text-sm text-muted-foreground">
      <span>
        Hiển thị {{ pagination.from || 0 }} đến {{ pagination.to || 0 }} của {{ pagination.total || 0 }} người dùng
      </span>
      <div class="flex gap-2" v-if="pagination.last_page > 1">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page <= 1"
          @click="fetchUsers(pagination.current_page - 1)"
        >
          Trước
        </Button>
        <span class="px-3 py-1 text-sm">
          Trang {{ pagination.current_page }} / {{ pagination.last_page }}
        </span>
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page >= pagination.last_page"
          @click="fetchUsers(pagination.current_page + 1)"
        >
          Sau
        </Button>
      </div>
    </div>
  </div>
</template>
