<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class UserController extends Controller
{
    use ApiResponseTrait;

    public function __construct()
    {
        // Áp dụng middleware permission để kiểm tra quyền quản lý người dùng
        $this->middleware('permission:manage_users')->only(['index']);
    }

    /**
     * Hiển thị danh sách người dùng với phân trang và bộ lọc.
     */
    public function index(Request $request)
    {
        try {
            $filters = $request->only(['search', 'role', 'status']);
            $perPage = $request->input('per_page', 15);
            $currentPage = $request->input('page', 1);

            $cacheKey = 'users_list_' . md5(json_encode($filters) . $perPage . $currentPage);
            $cacheTag = 'users-list';

            // Sử dụng closure cho logic truy vấn để truyền vào cache
            $queryCallback = function () use ($filters, $perPage) {
                $query = User::query()->with(['roles', 'permissions']);

                // Áp dụng bộ lọc tìm kiếm có điều kiện
                $query->when(!empty($filters['search']), function ($q) use ($filters) {
                    $searchTerm = '%' . $filters['search'] . '%';
                    return $q->where(function ($subQ) use ($searchTerm) {
                        $subQ->where('name', 'ilike', $searchTerm)
                             ->orWhere('email', 'ilike', $searchTerm);
                    });
                });

                // Áp dụng bộ lọc vai trò (role) trên relationship
                $query->when(!empty($filters['role']), function ($q) use ($filters) {
                    return $q->whereHas('roles', function ($subQ) use ($filters) {
                        $subQ->where('name', $filters['role']);
                    });
                });

                // Áp dụng bộ lọc trạng thái (status)
                $query->when(!empty($filters['status']), function ($q) use ($filters) {
                    return $q->where('status', $filters['status']);
                });

                return $query->latest()->paginate($perPage);
            };

            // Lấy từ cache hoặc thực thi truy vấn và lưu kết quả vào cache
            $users = Cache::tags($cacheTag)->remember($cacheKey, 3600, $queryCallback);

            // Sử dụng paginated resource collection để trả về response
            return UserResource::collection($users)
                ->additional([
                    'success' => true,
                    'message' => 'Lấy danh sách người dùng thành công',
                ]);

        } catch (Throwable $e) {
            Log::error('Lỗi khi lấy danh sách người dùng: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách người dùng', 500);
        }
    }
}
